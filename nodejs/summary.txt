title: "[EPIC] DIY Previews: ArgoCD ApplicationSet + Crossplane + vCluster"
description: |

Background

Build an internal, GitOps-based preview environment system tied to PRs.

Acceptance Criteria

Working blueprint: PR → vCluster (or namespace) → Helm deploy of services.

Crossplane provisions per-env SQS + Aurora clone or RDS snapshot restore.

Ingress + DNS per PR (pr-<id>.env.company.com).

Auto-cleanup on PR merged/closed; TTL sweeper for strays.


title: "[TASK] ArgoCD ApplicationSet PR Generator"
description: |

Background

Use ApplicationSet PR generator to spin a namespace or vCluster per PR.

Acceptance Criteria

PR creates namespace/vCluster and deploys He<PERSON> for at least 3 services.

Preview URL accessible; updates on new commits.

Auto-delete on PR close/merge.


title: "[TASK] vCluster per PR for isolation"
description: |

Background

Improve isolation and reduce cross-namespace blast radius by using vCluster.

Acceptance Criteria

GitHub Actions workflow that creates/destroys a vCluster per PR.

Flux/Argo applies manifests into the vCluster.

Smoke tests pass; teardown frees resources.


title: "[TASK] Crossplane AWS providers (SQS, RDS/Aurora)"
description: |

Background

Manage AWS dependencies as K8s CRDs for each preview env.

Acceptance Criteria

Crossplane providers installed; IRSA in place.

Composite or Managed resources to create SQS queues with PR suffix and Aurora clone/RDS restore from snapshot.

Finalizers ensure clean teardown.


title: "[TASK] DB cloning & data seeding"
description: |

Background

Fast DB provisioning per PR via Aurora Fast Cloning (preferred) or RDS snapshot restore with anonymization.

Acceptance Criteria

Aurora clone created in <5 minutes with seeded schema/data, or automated snapshot restore job with masking.

Init job/migration step runs on env creation.

Teardown removes clone/snapshot env cleanly.


title: "[TASK] SQS per-env strategy"
description: |

Background

Per-env queues with strict IAM policies.

Acceptance Criteria

Naming convention queue-<service>-pr-<id>.

Publisher/consumer IAM roles scoped to env.

Smoke test proving isolation between PRs.


title: "[TASK] Domain, TLS, and preview URLs"
description: |

Background

Ingress per PR with DNS automation.

Acceptance Criteria

DNS wildcard or automated record per PR.

TLS certs provisioned automatically.

Links posted back to PR.


title: "[TASK] Cleanup / TTL controller"
description: |

Background

Prevent orphaned resources.

Acceptance Criteria

PR close/merge triggers teardown workflow.

Scheduled TTL sweeper deletes envs older than N days.

Cost dashboard for preview envs.
